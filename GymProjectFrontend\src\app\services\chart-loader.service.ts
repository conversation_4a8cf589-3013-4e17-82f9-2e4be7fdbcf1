import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class ChartLoaderService {
  private chartLoaded = false;

  async loadChart(): Promise<any> {
    if (this.chartLoaded) {
      return (window as any).Chart;
    }

    try {
      const { Chart, registerables } = await import('chart.js');
      Chart.register(...registerables);
      (window as any).Chart = Chart;
      this.chartLoaded = true;
      return Chart;
    } catch (error) {
      console.error('Chart.js yüklenirken hata o<PERSON>:', error);
      throw error;
    }
  }
}
